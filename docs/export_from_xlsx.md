| 对话过程 | AssistantAgent | CompressedAssistantAgent | ReActAgent | RealQueryAgent |
| --- | --- | --- | --- | --- |
| user：你好 | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>reply：<br>你好，有什么可以帮助你的吗？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>reply：<br>你好，有什么可以帮助你的吗？ | Thinking:<br>Thought: 用户当前发送了一个‘你好’，...<br>Final Answer: 你好，有什么可以帮助你的吗？<br>memory：<br>不存储<br>reply：<br>你好，有什么可以帮助你的吗？ | Thinking:<br>reply：你好。<br>check：YES<br>reply：<br>你好。 |
| user：请帮我查询一下数据库中有多少用户 | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>reply：<br>好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>reply：<br>好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？ | Thinking:<br>Thought: 用户需要我查询数据库，并统计数据库中有多少用户，首先，我应该调用工具...<br>Action: CDPSqlTool<br>Action Input：{"sql": "select ..."}<br>Observation：<这里是数据查询结果><br>Thought：我已经查询完数据，从数据中可以看出用户总数为...<br>Final Answer: 数据库中的用户总数为10238<br>memory：<br>不存储<br>reply：<br>数据库中的用户总数为10238 | Thinking:<br>reply: 请查询数据库，并统计有多少用户<br>check: NO，原因是数据库不明确<br>reply: 请查询CDP数据库的 users 表，并统计用户总数<br>check：YES<br>reply：<br>请查询CDP数据库的 users 表，并统计用户总数 |
| user: 这些用户的性别比例是多少？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>user: 请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>user: 这些用户的性别比例是多少？<br>assistant: 由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计<br>reply：<br>由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计 | memory（假设触发压缩）：<br>assistant：用户首先问好，然后问了数据库用户数问题，已回复由于能力不足无法查询<br>user: 这些用户的性别比例是多少？<br>assistant: 由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计<br>reply：<br>由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计 | Thinking:<br>Thought: 现在需要查询所有用户的性别比例，首先，我应该调用工具...<br>Action: CDPSqlTool<br>Action Input：{"sql": "select ..."}<br>Observation：<这里是数据查询结果><br>Thought：我已经查询完数据，从数据中可以看出用户总数为...<br>Final Answer: 男生人数为 500人，占比为 xx%，女生人数为500人，占比为 xx%，未知性别人数为 ...<br>memory：<br>不存储<br>reply：<br>男生人数为 500人，占比为 xx%，女生人数为500人，占比为 xx%，未知性别人数为 ... | Thinking:<br>reply: 请查询CDP数据库的 users 表，并统计这些用户的性别比例<br>check: YES<br>reply：<br>请查询CDP数据库的 users 表，并统计这些用户的性别比例 |
